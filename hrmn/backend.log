Sourcing setup_env.sh from hailo-rpi5-examples...
Setting up the environment...
Setting up the environment for hailo-tappas-core...
TAPPAS_VERSION is 3.31.0. Proceeding...
You are not in the venv_hailo_rpi5_examples virtual environment.
Virtual environment exists. Activating...
TAPPAS_POST_PROC_DIR set to /usr/lib/aarch64-linux-gnu/hailo/tappas/post_processes
HailoRT warning: Cannot create log file hailort.log! Please check the file /home/<USER>/.hailo/hailort/hailort.log write permissions.
[HailoRT] [error] CHECK failed - Failed to open device file /dev/hailo0 with error 2
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
[HailoRT CLI] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
Error: Device Architecture not found. Please check the connection to the device.
[CONFIG] Using video sink: fakesink
[CONFIG] Set GST_AUTOVIDEOSINK=fakesink for performance mode
[INFO] schreibe Pose-Daten in SHM='pose_shm' - ECHTZEIT OPTIMIERT
[CONFIG] Video sink mode: fakesink
[CONFIG] Applying fakesink monkey-patch for performance mode
[MONKEY-PATCH] GStreamer ElementFactory gepatcht für fakesink!
Error running hailortcli: HailoRT warning: Cannot create log file hailort.log! Please check the file /home/<USER>/.hailo/hailort/hailort.log write permissions.
[HailoRT] [error] CHECK failed - Failed to open device file /dev/hailo0 with error 2
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
[HailoRT CLI] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)

Traceback (most recent call last):
  File "/home/<USER>/Documents/taha_ai/hrmn/pose_estimation.py", line 136, in <module>
    app = GStreamerPoseEstimationApp(app_callback, user_data)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/taha_ai/hailo-rpi5-examples/venv_hailo_rpi5_examples/lib/python3.11/site-packages/hailo_apps_infra/pose_estimation_pipeline.py", line 54, in __init__
    raise ValueError("Could not auto-detect Hailo architecture. Please specify --arch manually.")
ValueError: Could not auto-detect Hailo architecture. Please specify --arch manually.
/usr/lib/python3.11/multiprocessing/resource_tracker.py:224: UserWarning: resource_tracker: There appear to be 1 leaked shared_memory objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
